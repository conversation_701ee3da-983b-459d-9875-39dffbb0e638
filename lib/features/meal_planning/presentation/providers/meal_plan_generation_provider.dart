import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../shared/providers/auth_provider.dart';
import '../../data/models/meal_plan_request.dart';
import '../../data/services/meal_plan_service.dart';

part 'meal_plan_generation_provider.freezed.dart';
part 'meal_plan_generation_provider.g.dart';

@freezed
class MealPlanGenerationState with _$MealPlanGenerationState {
  const factory MealPlanGenerationState({
    @Default(false) bool isLoading,
    @Default(false) bool isGenerating,
    MealPlanResponse? generatedPlan,
    String? error,
    @Default(MealPlanPreferences()) MealPlanPreferences preferences,
    @Default(3) int duration,
    @Default(false) bool showAdvancedSettings,
  }) = _MealPlanGenerationState;
}

@riverpod
class MealPlanGenerationNotifier extends _$MealPlanGenerationNotifier {
  @override
  MealPlanGenerationState build() {
    return const MealPlanGenerationState();
  }

  /// Update diet type
  void updateDietType(DietType dietType) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(dietType: dietType),
    );
  }

  /// Update calorie goal
  void updateCalorieGoal(int calories) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(calorieGoal: calories),
    );
  }

  /// Update protein goal
  void updateProteinGoal(int protein) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(proteinGoal: protein),
    );
  }

  /// Update carbs goal
  void updateCarbsGoal(int carbs) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(carbsGoal: carbs),
    );
  }

  /// Update fat goal
  void updateFatGoal(int fat) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(fatGoal: fat),
    );
  }

  /// Update meals per day
  void updateMealsPerDay(int meals) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(mealsPerDay: meals),
    );
  }

  /// Update duration
  void updateDuration(int duration) {
    state = state.copyWith(duration: duration);
  }

  /// Toggle advanced settings visibility
  void toggleAdvancedSettings() {
    state = state.copyWith(showAdvancedSettings: !state.showAdvancedSettings);
  }

  /// Add dietary restriction
  void addDietaryRestriction(String restriction) {
    final restrictions = List<String>.from(state.preferences.dietaryRestrictions);
    if (!restrictions.contains(restriction)) {
      restrictions.add(restriction);
      state = state.copyWith(
        preferences: state.preferences.copyWith(dietaryRestrictions: restrictions),
      );
    }
  }

  /// Remove dietary restriction
  void removeDietaryRestriction(String restriction) {
    final restrictions = List<String>.from(state.preferences.dietaryRestrictions);
    restrictions.remove(restriction);
    state = state.copyWith(
      preferences: state.preferences.copyWith(dietaryRestrictions: restrictions),
    );
  }

  /// Add allergy
  void addAllergy(String allergy) {
    final allergies = List<String>.from(state.preferences.allergies);
    if (!allergies.contains(allergy)) {
      allergies.add(allergy);
      state = state.copyWith(
        preferences: state.preferences.copyWith(allergies: allergies),
      );
    }
  }

  /// Remove allergy
  void removeAllergy(String allergy) {
    final allergies = List<String>.from(state.preferences.allergies);
    allergies.remove(allergy);
    state = state.copyWith(
      preferences: state.preferences.copyWith(allergies: allergies),
    );
  }

  /// Add cuisine preference
  void addCuisinePreference(String cuisine) {
    final cuisines = List<String>.from(state.preferences.cuisinePreferences);
    if (!cuisines.contains(cuisine)) {
      cuisines.add(cuisine);
      state = state.copyWith(
        preferences: state.preferences.copyWith(cuisinePreferences: cuisines),
      );
    }
  }

  /// Remove cuisine preference
  void removeCuisinePreference(String cuisine) {
    final cuisines = List<String>.from(state.preferences.cuisinePreferences);
    cuisines.remove(cuisine);
    state = state.copyWith(
      preferences: state.preferences.copyWith(cuisinePreferences: cuisines),
    );
  }

  /// Generate meal plan
  /// Preferences are automatically retrieved from user profile in Firestore
  Future<void> generateMealPlan() async {
    // Testing flag - set to true to use mock data instead of API call
    const bool useMockData = true;

    final userProfile = ref.read(currentUserProfileProvider);
    if (userProfile == null) {
      state = state.copyWith(error: 'User not authenticated');
      return;
    }

    state = state.copyWith(
      isGenerating: true,
      error: null,
      generatedPlan: null,
    );

    try {
      MealPlanResponse response;

      if (useMockData) {
        // Return mock data for testing
        response = _createMockMealPlanResponse();
      } else {
        final mealPlanService = ref.read(mealPlanServiceProvider);
        response = await mealPlanService.generateMealPlan(
          duration: state.duration,
        );
      }

      state = state.copyWith(
        isGenerating: false,
        generatedPlan: response,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
      );
    }
  }

  /// Generate extra 3 days for the existing meal plan
  Future<void> generateExtraDays() async {
    const bool useMockData = true;

    final currentPlan = state.generatedPlan;
    if (currentPlan == null) {
      state = state.copyWith(error: 'No existing meal plan to extend');
      return;
    }

    final userProfile = ref.read(currentUserProfileProvider);
    if (userProfile == null) {
      state = state.copyWith(error: 'User not authenticated');
      return;
    }

    state = state.copyWith(isGenerating: true, error: null);

    try {
      List<DayMealPlan> extraDays;

      if (useMockData) {
        // Generate mock extra days
        extraDays = _createMockExtraDays(currentPlan.mealPlan.days.length);
      } else {
        final mealPlanService = ref.read(mealPlanServiceProvider);
        final response = await mealPlanService.generateMealPlan(duration: 3);
        extraDays = response.mealPlan.days;
      }

      // Add extra days to existing plan
      final updatedDays = [...currentPlan.mealPlan.days, ...extraDays];
      final updatedPlan = currentPlan.copyWith(
        mealPlan: currentPlan.mealPlan.copyWith(days: updatedDays),
      );

      state = state.copyWith(
        isGenerating: false,
        generatedPlan: updatedPlan,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
      );
    }
  }

  /// Creates a mock meal plan response for testing
  MealPlanResponse _createMockMealPlanResponse() {
    final now = DateTime.now();

    return MealPlanResponse(
      success: true,
      message: 'خطة وجبات تم إنشاؤها بنجاح (بيانات تجريبية)',
      mealPlan: GeneratedMealPlan(
        days: [
          // Day 1
          DayMealPlan(
            day: 1,
            totalNutrition: const NutritionInfo(
              calories: 2100,
              protein: 120.0,
              carbs: 250.0,
              fat: 70.0,
              fiber: 35.0,
            ),
            meals: [
              GeneratedMeal(
                name: 'شوفان بالفواكه والمكسرات',
                description: 'وجبة إفطار صحية ومغذية غنية بالألياف والبروتين',
                type: 'breakfast',
                ingredients: [
                  '½ كوب شوفان',
                  '1 كوب حليب قليل الدسم',
                  '½ موزة مقطعة شرائح',
                  '2 ملعقة كبيرة توت أزرق',
                  '1 ملعقة كبيرة عسل',
                  '2 ملعقة كبيرة لوز مقطع',
                  'رشة قرفة',
                ],
                instructions: [
                  'اغلي الحليب في مقلاة متوسطة الحجم',
                  'أضف الشوفان واتركه ينضج لمدة 5-7 دقائق مع التحريك المستمر',
                  'أضف القرفة والعسل وحرك جيداً',
                  'اسكب الخليط في وعاء التقديم',
                  'زين بشرائح الموز والتوت الأزرق واللوز المقطع',
                  'قدم ساخناً واستمتع بوجبة إفطار صحية',
                ],
                nutrition: const NutritionInfo(
                  calories: 420,
                  protein: 18.0,
                  carbs: 65.0,
                  fat: 12.0,
                  fiber: 8.0,
                ),
                preparationTime: 15,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'سلطة الدجاج المشوي مع الكينوا',
                description: 'وجبة غداء متوازنة غنية بالبروتين والخضار الطازجة',
                type: 'lunch',
                ingredients: [
                  '150 جرام صدر دجاج مشوي',
                  '½ كوب كينوا مطبوخة',
                  '2 كوب خضار ورقية مشكلة',
                  '½ كوب طماطم كرزية',
                  '¼ كوب خيار مقطع مكعبات',
                  '¼ كوب جزر مبشور',
                  '2 ملعقة كبيرة زيت زيتون',
                  '1 ملعقة كبيرة عصير ليمون',
                  'ملح وفلفل أسود حسب الذوق',
                ],
                instructions: [
                  'تبل صدر الدجاج بالملح والفلفل واشويه حتى ينضج تماماً',
                  'اتركه يبرد ثم قطعه شرائح رفيعة',
                  'اطبخ الكينوا حسب التعليمات على العبوة واتركها تبرد',
                  'في وعاء كبير، اخلط الخضار الورقية مع الطماطم والخيار والجزر',
                  'أضف الكينوا المطبوخة وشرائح الدجاج',
                  'اخلط زيت الزيتون مع عصير الليمون والملح والفلفل',
                  'اسكب الصلصة على السلطة وحرك جيداً قبل التقديم',
                ],
                nutrition: const NutritionInfo(
                  calories: 580,
                  protein: 45.0,
                  carbs: 35.0,
                  fat: 28.0,
                  fiber: 12.0,
                ),
                preparationTime: 25,
                difficulty: 'medium',
              ),
              GeneratedMeal(
                name: 'سمك السلمون المشوي مع الخضار',
                description: 'وجبة عشاء صحية غنية بأوميجا 3 والخضار المشوية',
                type: 'dinner',
                ingredients: [
                  '150 جرام فيليه سلمون',
                  '1 كوب بروكلي',
                  '½ كوب جزر مقطع',
                  '½ كوب كوسا مقطعة',
                  '2 ملعقة كبيرة زيت زيتون',
                  '1 ملعقة صغيرة ثوم مفروم',
                  'عصير ½ ليمونة',
                  'ملح وفلفل وأعشاب طازجة',
                ],
                instructions: [
                  'سخن الفرن على 200 درجة مئوية',
                  'تبل السلمون بالملح والفلفل وعصير الليمون',
                  'قطع الخضار قطع متوسطة وتبلها بزيت الزيتون والثوم',
                  'ضع السلمون والخضار في صينية الفرن',
                  'اشو لمدة 15-20 دقيقة حتى ينضج السلمون',
                  'زين بالأعشاب الطازجة وقدم ساخناً',
                ],
                nutrition: const NutritionInfo(
                  calories: 450,
                  protein: 35.0,
                  carbs: 20.0,
                  fat: 25.0,
                  fiber: 8.0,
                ),
                preparationTime: 30,
                difficulty: 'medium',
              ),
              GeneratedMeal(
                name: 'زبادي يوناني بالتوت',
                description: 'وجبة خفيفة صحية غنية بالبروتين والبروبيوتيك',
                type: 'snack',
                ingredients: [
                  '1 كوب زبادي يوناني قليل الدسم',
                  '½ كوب توت مشكل',
                  '1 ملعقة كبيرة عسل',
                  '2 ملعقة كبيرة جوز مقطع',
                  'رشة فانيليا',
                ],
                instructions: [
                  'ضع الزبادي في وعاء التقديم',
                  'أضف العسل والفانيليا وحرك جيداً',
                  'زين بالتوت المشكل والجوز المقطع',
                  'قدم بارداً',
                ],
                nutrition: const NutritionInfo(
                  calories: 280,
                  protein: 20.0,
                  carbs: 25.0,
                  fat: 12.0,
                  fiber: 4.0,
                ),
                preparationTime: 5,
                difficulty: 'easy',
              ),
            ],
          ),
          // Day 2
          DayMealPlan(
            day: 2,
            totalNutrition: const NutritionInfo(
              calories: 2050,
              protein: 115.0,
              carbs: 240.0,
              fat: 68.0,
              fiber: 32.0,
            ),
            meals: [
              GeneratedMeal(
                name: 'عجة الخضار بالجبن',
                description: 'إفطار غني بالبروتين والخضار الطازجة',
                type: 'breakfast',
                ingredients: [
                  '3 بيضات كاملة',
                  '¼ كوب سبانخ مقطعة',
                  '¼ كوب طماطم مقطعة مكعبات',
                  '¼ كوب فلفل ملون مقطع',
                  '30 جرام جبن قليل الدسم',
                  '1 ملعقة كبيرة زيت زيتون',
                  'ملح وفلفل أسود',
                ],
                instructions: [
                  'اخفق البيض مع الملح والفلفل',
                  'سخن زيت الزيتون في مقلاة غير لاصقة',
                  'أضف الخضار واقليها لدقيقتين',
                  'اسكب البيض المخفوق فوق الخضار',
                  'أضف الجبن واتركها تنضج من الجهتين',
                  'قدم ساخنة مع شريحة خبز أسمر',
                ],
                nutrition: const NutritionInfo(
                  calories: 380,
                  protein: 25.0,
                  carbs: 15.0,
                  fat: 26.0,
                  fiber: 4.0,
                ),
                preparationTime: 15,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'شوربة العدس مع الخبز الأسمر',
                description: 'وجبة غداء مغذية وغنية بالبروتين النباتي',
                type: 'lunch',
                ingredients: [
                  '1 كوب عدس أحمر',
                  '2 كوب مرق خضار',
                  '½ كوب جزر مقطع مكعبات',
                  '½ كوب بصل مقطع',
                  '2 ملعقة كبيرة زيت زيتون',
                  '1 ملعقة صغيرة كمون',
                  'ملح وفلفل حسب الذوق',
                  '2 شريحة خبز أسمر',
                ],
                instructions: [
                  'اغسل العدس جيداً واتركه ينقع لـ 30 دقيقة',
                  'في مقلاة كبيرة، اقلي البصل بزيت الزيتون حتى يذبل',
                  'أضف الجزر والكمون وحرك لدقيقتين',
                  'أضف العدس والمرق واتركه ينضج لـ 25 دقيقة',
                  'تبل بالملح والفلفل',
                  'قدم ساخناً مع الخبز الأسمر',
                ],
                nutrition: const NutritionInfo(
                  calories: 520,
                  protein: 28.0,
                  carbs: 75.0,
                  fat: 15.0,
                  fiber: 18.0,
                ),
                preparationTime: 45,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'دجاج مشوي بالأعشاب مع الأرز البني',
                description: 'وجبة عشاء متوازنة ولذيذة',
                type: 'dinner',
                ingredients: [
                  '150 جرام صدر دجاج',
                  '½ كوب أرز بني مطبوخ',
                  '1 كوب خضار مشكلة مطبوخة على البخار',
                  '2 ملعقة كبيرة زيت زيتون',
                  '1 ملعقة صغيرة أعشاب مجففة',
                  'ملح وفلفل أسود',
                ],
                instructions: [
                  'تبل الدجاج بالأعشاب والملح والفلفل',
                  'اشو الدجاج في الفرن على 180 درجة لـ 25 دقيقة',
                  'اطبخ الأرز البني حسب التعليمات',
                  'اطبخ الخضار على البخار حتى تنضج',
                  'قدم الدجاج مع الأرز والخضار',
                ],
                nutrition: const NutritionInfo(
                  calories: 480,
                  protein: 42.0,
                  carbs: 45.0,
                  fat: 18.0,
                  fiber: 6.0,
                ),
                preparationTime: 35,
                difficulty: 'medium',
              ),
              GeneratedMeal(
                name: 'تفاحة مع زبدة اللوز',
                description: 'وجبة خفيفة صحية ومشبعة',
                type: 'snack',
                ingredients: [
                  '1 تفاحة متوسطة',
                  '2 ملعقة كبيرة زبدة لوز طبيعية',
                  'رشة قرفة',
                ],
                instructions: [
                  'اغسل التفاحة وقطعها شرائح',
                  'ادهن كل شريحة بزبدة اللوز',
                  'رش القرفة حسب الرغبة',
                  'قدم فوراً',
                ],
                nutrition: const NutritionInfo(
                  calories: 250,
                  protein: 8.0,
                  carbs: 25.0,
                  fat: 15.0,
                  fiber: 6.0,
                ),
                preparationTime: 5,
                difficulty: 'easy',
              ),
            ],
          ),
          // Day 3
          DayMealPlan(
            day: 3,
            totalNutrition: const NutritionInfo(
              calories: 2080,
              protein: 118.0,
              carbs: 245.0,
              fat: 72.0,
              fiber: 30.0,
            ),
            meals: [
              GeneratedMeal(
                name: 'سموثي الفواكه والخضار',
                description: 'مشروب إفطار منعش ومغذي',
                type: 'breakfast',
                ingredients: [
                  '1 موزة',
                  '½ كوب توت مجمد',
                  '1 كوب سبانخ طازجة',
                  '1 كوب حليب لوز',
                  '1 ملعقة كبيرة بذور الشيا',
                  '1 ملعقة كبيرة عسل',
                ],
                instructions: [
                  'ضع جميع المكونات في الخلاط',
                  'اخلط حتى يصبح القوام ناعماً',
                  'أضف الثلج إذا رغبت في قوام أكثر برودة',
                  'اسكب في كوب التقديم',
                  'زين ببذور الشيا الإضافية',
                ],
                nutrition: const NutritionInfo(
                  calories: 320,
                  protein: 12.0,
                  carbs: 55.0,
                  fat: 8.0,
                  fiber: 12.0,
                ),
                preparationTime: 10,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'سلطة التونة مع الحمص',
                description: 'وجبة غداء خفيفة وغنية بالبروتين',
                type: 'lunch',
                ingredients: [
                  '1 علبة تونة في الماء (150 جرام)',
                  '½ كوب حمص مسلوق',
                  '2 كوب خضار ورقية',
                  '½ كوب خيار مقطع',
                  '¼ كوب بصل أحمر مقطع',
                  '2 ملعقة كبيرة زيت زيتون',
                  '1 ملعقة كبيرة خل بلسمي',
                  'ملح وفلفل',
                ],
                instructions: [
                  'صفي التونة من الماء وفككها بالشوكة',
                  'اخلط الخضار الورقية مع الخيار والبصل',
                  'أضف التونة والحمص',
                  'اخلط زيت الزيتون مع الخل والملح والفلفل',
                  'اسكب الصلصة على السلطة وحرك جيداً',
                ],
                nutrition: const NutritionInfo(
                  calories: 450,
                  protein: 35.0,
                  carbs: 30.0,
                  fat: 22.0,
                  fiber: 8.0,
                ),
                preparationTime: 15,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'لحم بقري مشوي مع البطاطا الحلوة',
                description: 'وجبة عشاء غنية بالحديد والفيتامينات',
                type: 'dinner',
                ingredients: [
                  '120 جرام لحم بقري قليل الدهن',
                  '1 حبة بطاطا حلوة متوسطة',
                  '1 كوب فاصولياء خضراء',
                  '2 ملعقة كبيرة زيت زيتون',
                  '1 ملعقة صغيرة روزماري',
                  'ملح وفلفل أسود',
                ],
                instructions: [
                  'سخن الفرن على 200 درجة مئوية',
                  'قطع البطاطا الحلوة مكعبات وتبلها بزيت الزيتون',
                  'اشو البطاطا لـ 25 دقيقة',
                  'تبل اللحم بالروزماري والملح والفلفل',
                  'اشو اللحم في مقلاة ساخنة لـ 8 دقائق',
                  'اطبخ الفاصولياء على البخار وقدم الوجبة',
                ],
                nutrition: const NutritionInfo(
                  calories: 520,
                  protein: 38.0,
                  carbs: 45.0,
                  fat: 22.0,
                  fiber: 8.0,
                ),
                preparationTime: 40,
                difficulty: 'medium',
              ),
              GeneratedMeal(
                name: 'مكسرات وفواكه مجففة',
                description: 'وجبة خفيفة طبيعية ومغذية',
                type: 'snack',
                ingredients: [
                  '30 جرام لوز نيء',
                  '20 جرام تمر مجفف',
                  '10 جرام جوز',
                ],
                instructions: [
                  'امزج المكسرات والفواكه المجففة',
                  'قدم في وعاء صغير',
                  'تناول كوجبة خفيفة بين الوجبات',
                ],
                nutrition: const NutritionInfo(
                  calories: 280,
                  protein: 8.0,
                  carbs: 22.0,
                  fat: 18.0,
                  fiber: 5.0,
                ),
                preparationTime: 2,
                difficulty: 'easy',
              ),
            ],
          ),
        ],
        rawResponse: 'Mock meal plan generated for testing purposes',
      ),
    );
  }

  /// Reset state
  void reset() {
    state = const MealPlanGenerationState();
  }

  /// Load user preferences from profile
  void loadUserPreferences() {
    final userProfile = ref.read(currentUserProfileProvider);
    if (userProfile == null) return;

    state = state.copyWith(
      preferences: state.preferences.copyWith(
        calorieGoal: userProfile.dailyCalorieGoal ?? 2000,
        proteinGoal: userProfile.proteinGoal?.toInt() ?? 0,
        carbsGoal: userProfile.carbsGoal?.toInt() ?? 0,
        fatGoal: userProfile.fatGoal?.toInt() ?? 0,
        mealsPerDay: userProfile.mealsPerDay,
        dietaryRestrictions: userProfile.dietaryRestrictions,
        allergies: userProfile.allergies,
      ),
    );
  }
}
