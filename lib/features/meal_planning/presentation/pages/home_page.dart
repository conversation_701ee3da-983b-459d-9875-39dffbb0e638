import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';

import '../../../../generated/l10n/app_localizations.dart';
import '../../data/models/meal_plan_request.dart';

import '../providers/meal_plan_generation_provider.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      body: const _HomeTab(),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          if (index == 1) {
            // Navigate to profile page
            GoRouter.of(context).push('/profile');
          } else {
            // Stay on home tab
            setState(() => _currentIndex = 0);
          }
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home),
            label: l10n.home,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person),
            label: l10n.profile,
          ),
        ],
      ),
    );
  }
}

class _HomeTab extends ConsumerStatefulWidget {
  const _HomeTab();

  @override
  ConsumerState<_HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends ConsumerState<_HomeTab> {
  late PageController _pageController;
  int _currentPageIndex = 0;
  bool _isGeneratingExtraDays = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    // Load user preferences when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(mealPlanGenerationNotifierProvider.notifier).loadUserPreferences();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    final mealPlanState = ref.watch(mealPlanGenerationNotifierProvider);


    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.appName),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
        ],
      ),
      body: mealPlanState.isGenerating
        ? _buildGeneratingView()
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
            // Welcome Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحباً بك!',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ابدأ يومك بخطة غذائية صحية',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Error Display
            if (mealPlanState.error != null) ...[
              _buildErrorCard(mealPlanState.error!),
              const SizedBox(height: 24),
            ],

            // Meal Planning Section
            Text(
              'خطة الوجبات',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // PageView for meal plans or generation
            _buildMealPlanPageView(mealPlanState.generatedPlan),
          ],
        ),
      ),
    );
  }



  /// Start meal plan generation immediately using saved preferences
  void _startMealPlanGeneration(MealPlanGenerationNotifier notifier) {
    // Reset page controller and current page
    setState(() {
      _currentPageIndex = 0;
      _isGeneratingExtraDays = false;
    });

    // Load user preferences and start generation
    notifier.loadUserPreferences();
    notifier.generateMealPlan();
  }

  /// Generate extra 3 days for the meal plan
  Future<void> _generateExtraDays() async {
    setState(() {
      _isGeneratingExtraDays = true;
    });

    try {
      final currentPlan = ref.read(mealPlanGenerationNotifierProvider).generatedPlan;
      final currentDaysCount = currentPlan?.mealPlan.days.length ?? 0;

      final notifier = ref.read(mealPlanGenerationNotifierProvider.notifier);
      await notifier.generateExtraDays();

      // Navigate to the first new day after generation
      if (mounted) {
        await Future.delayed(const Duration(milliseconds: 500));
        _pageController.animateToPage(
          currentDaysCount, // Navigate to the first new day (index starts from 0)
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء أيام إضافية: $e'),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingExtraDays = false;
        });
      }
    }
  }

  Widget _buildGeneratingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // AI Logo/Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.auto_awesome,
              size: 60,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 32),

          // Loading animation
          SizedBox(
            width: 40,
            height: 40,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),
          const SizedBox(height: 24),

          // Loading text
          Text(
            'جاري إنشاء خطة وجبات مخصصة لك...',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            'يتم تحليل تفضيلاتك وإنشاء خطة مثالية باستخدام الذكاء الاصطناعي',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCard(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حدث خطأ',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  error,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealPlanPageView(MealPlanResponse? plan) {
    // If no plan exists, show only the generation page
    if (plan == null) {
      return SizedBox(
        height: 600,
        child: _buildInitialGenerationCard(),
      );
    }

    final totalDays = plan.mealPlan.days.length;


    return Column(
      children: [
        // Page indicator and navigation
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Navigation button (left)
            IconButton(
              onPressed: _currentPageIndex > 0 ? () {
                _pageController.previousPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              } : null,
              icon: Icon(
                Icons.arrow_back_ios,
                color: _currentPageIndex > 0 ? AppColors.primary : Colors.grey,
              ),
            ),

            // Page indicator
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (int i = 0; i < totalDays + 1; i++) // +1 for confirmation page
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: i == _currentPageIndex
                          ? AppColors.primary
                          : Colors.grey.shade300,
                      ),
                    ),
                  // Show loading indicator if generating extra days
                  if (_isGeneratingExtraDays)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    ),
                ],
              ),
            ),

            // Navigation button (right)
            IconButton(
              onPressed: _currentPageIndex < totalDays ? () {
                _pageController.nextPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              } : null,
              icon: Icon(
                Icons.arrow_forward_ios,
                color: _currentPageIndex < totalDays ? AppColors.primary : Colors.grey,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // PageView for meal plan days
        SizedBox(
          height: 600, // Fixed height for the PageView
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPageIndex = index;
              });

              // Check if we reached the last actual day and need to show confirmation page
              if (index == totalDays && !_isGeneratingExtraDays) {
                // This will be the confirmation page
              }
            },
            itemCount: totalDays + 1, // Add one for the confirmation page
            itemBuilder: (context, index) {
              if (index < totalDays) {
                // Regular day card
                final day = plan.mealPlan.days[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: _buildDayMealCard(day),
                );
              } else {
                // Confirmation page for generating extra days
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: _buildGenerateExtraDaysCard(),
                );
              }
            },
          ),
        ),

        const SizedBox(height: 16),

        // Day counter and info
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            _currentPageIndex < totalDays
              ? 'اليوم ${_currentPageIndex + 1} من ${totalDays}'
              : 'إضافة أيام جديدة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInitialGenerationCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.auto_awesome,
              size: 50,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 32),

          // Title
          Text(
            'إنشاء خطة وجبات ذكية',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'احصل على خطة وجبات مخصصة لك لمدة 3 أيام باستخدام الذكاء الاصطناعي\nوجبات متنوعة ومتوازنة تناسب احتياجاتك الغذائية',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 40),

          // Generate Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _startMealPlanGeneration(ref.read(mealPlanGenerationNotifierProvider.notifier)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: Icon(Icons.restaurant_menu, size: 24),
                label: Text(
                  'إنشاء خطة الوجبات',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Features list
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Column(
              children: [
                _buildFeatureItem(Icons.schedule, '3 أيام من الوجبات المتنوعة'),
                const SizedBox(height: 8),
                _buildFeatureItem(Icons.restaurant, '4 وجبات يومياً (إفطار، غداء، عشاء، وجبة خفيفة)'),
                const SizedBox(height: 8),
                _buildFeatureItem(Icons.analytics, 'معلومات غذائية مفصلة لكل وجبة'),
                const SizedBox(height: 8),
                _buildFeatureItem(Icons.menu_book, 'طريقة تحضير مفصلة لكل وجبة'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.primary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGenerateExtraDaysCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.add_circle_outline,
              size: 40,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 24),

          // Title
          Text(
            'إضافة أيام جديدة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'هل تريد إضافة 3 أيام إضافية لخطة الوجبات الخاصة بك؟\nسيتم إنشاء وجبات جديدة ومتنوعة لك.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 32),

          // Generate Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isGeneratingExtraDays ? null : () => _generateExtraDays(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: _isGeneratingExtraDays
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(Icons.restaurant_menu),
                label: Text(
                  _isGeneratingExtraDays ? 'جاري الإنشاء...' : 'إنشاء 3 أيام إضافية',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Cancel/Back Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isGeneratingExtraDays ? null : () {
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.textSecondary,
                  side: BorderSide(color: AppColors.textSecondary),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: Icon(Icons.arrow_back),
                label: Text('العودة للخطة الحالية'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayMealCard(DayMealPlan day) {
    final now = DateTime.now();
    final dayDate = now.add(Duration(days: day.day - 1));
    final arabicDateFormat = DateFormat('EEEE، d MMMM yyyy', 'ar');

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day Header (Fixed)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${day.day}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'اليوم ${day.day}',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            arabicDateFormat.format(dayDate),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Nutrition Summary
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildNutritionItem('السعرات', '${day.totalNutrition.calories}', 'سعرة'),
                      _buildNutritionItem('البروتين', '${day.totalNutrition.protein.toStringAsFixed(0)}', 'جم'),
                      _buildNutritionItem('الكربوهيدرات', '${day.totalNutrition.carbs.toStringAsFixed(0)}', 'جم'),
                      _buildNutritionItem('الدهون', '${day.totalNutrition.fat.toStringAsFixed(0)}', 'جم'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Scrollable Meals Summary
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الوجبات (${day.meals.length})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),

                  ...day.meals.map((meal) => _buildMealSummaryItem(meal)).toList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionItem(String label, String value, String unit) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          unit,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildMealSummaryItem(GeneratedMeal meal) {
    IconData mealIcon;
    Color mealColor;

    switch (meal.type.toLowerCase()) {
      case 'breakfast':
        mealIcon = Icons.wb_sunny;
        mealColor = Colors.orange;
        break;
      case 'lunch':
        mealIcon = Icons.wb_sunny_outlined;
        mealColor = Colors.blue;
        break;
      case 'dinner':
        mealIcon = Icons.nightlight;
        mealColor = Colors.indigo;
        break;
      case 'snack':
        mealIcon = Icons.local_cafe;
        mealColor = Colors.green;
        break;
      default:
        mealIcon = Icons.restaurant;
        mealColor = Colors.grey;
    }

    return GestureDetector(
      onTap: () => _showMealDetailsDialog(meal),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: mealColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: mealColor.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: mealColor.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                mealIcon,
                color: mealColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getMealTypeDisplayName(meal.type),
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _getMealTypeColor(meal.type),
                    ),
                  ),
                  Text(
                    _buildFoodItemsList(meal),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  void _showMealDetailsDialog(GeneratedMeal meal) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: _getMealGradient(meal.type),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _getMealIcon(meal.type),
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getMealTypeDisplayName(meal.type),
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'قائمة الأطعمة',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: Icon(
                            Icons.close,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Quick info
                    Row(
                      children: [
                        _buildQuickInfo(Icons.restaurant, '${_getComplexItemsCount(meal)} عنصر'),
                        const SizedBox(width: 16),
                        _buildQuickInfo(Icons.schedule, '${meal.preparationTime} دقيقة'),
                        const SizedBox(width: 16),
                        _buildQuickInfo(Icons.local_fire_department, '${meal.nutrition.calories} سعرة'),
                      ],
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Food Items List
                      Text(
                        'عناصر الوجبة',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppColors.border),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: _buildFoodItemsDisplay(meal),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Nutrition Information
                      Text(
                        'المعلومات الغذائية',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppColors.border),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildNutritionDetail('السعرات', '${meal.nutrition.calories}', 'سعرة'),
                            _buildNutritionDetail('البروتين', '${meal.nutrition.protein.toStringAsFixed(1)}', 'جم'),
                            _buildNutritionDetail('الكربوهيدرات', '${meal.nutrition.carbs.toStringAsFixed(1)}', 'جم'),
                            _buildNutritionDetail('الدهون', '${meal.nutrition.fat.toStringAsFixed(1)}', 'جم'),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Complex Item Preparation (if any)
                      if (_hasComplexPreparation(meal)) ...[
                        Text(
                          'طريقة تحضير ${_getComplexItemName(meal)}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.orange.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.orange.shade600,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'العناصر الأخرى جاهزة للتقديم مباشرة',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.orange.shade700,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'المكونات المطلوبة:',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              ..._getComplexItemIngredients(meal).map((ingredient) => Padding(
                                padding: const EdgeInsets.only(bottom: 4),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 4,
                                      height: 4,
                                      decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        ingredient,
                                        style: Theme.of(context).textTheme.bodyMedium,
                                      ),
                                    ),
                                  ],
                                ),
                              )),
                              const SizedBox(height: 12),
                              Text(
                                'خطوات التحضير:',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              ..._getComplexItemInstructions(meal).asMap().entries.map((entry) => Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 20,
                                      height: 20,
                                      decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: Text(
                                          '${entry.key + 1}',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 10,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        entry.value,
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          height: 1.4,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )),
                            ],
                          ),
                        ),
                      ] else ...[
                        // No complex preparation needed
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.green.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.check_circle_outline,
                                color: Colors.green.shade600,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'جميع عناصر هذه الوجبة جاهزة للتقديم مباشرة - لا تحتاج تحضير!',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.green.shade700,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods for food items display
  String _buildFoodItemsList(GeneratedMeal meal) {
    // Display ingredients as simple food items
    if (meal.ingredients.length <= 3) {
      return meal.ingredients.join('، ');
    } else {
      return '${meal.ingredients.take(3).join('، ')}... و${meal.ingredients.length - 3} عنصر آخر';
    }
  }

  List<Widget> _buildFoodItemsDisplay(GeneratedMeal meal) {
    final foodItems = _convertIngredientsToFoodItems(meal.ingredients);
    final simpleItems = <String>[];
    final complexItems = <String>[];

    // Categorize items based on complexity
    for (String item in foodItems) {
      if (_isSimpleItem(item)) {
        simpleItems.add(item);
      } else {
        complexItems.add(item);
      }
    }

    List<Widget> widgets = [];

    // Show simple items (ready to eat)
    if (simpleItems.isNotEmpty) {
      widgets.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green.shade600,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'جاهز للتقديم: ${simpleItems.join('، ')}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.green.shade700,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Show complex items (need preparation)
    if (complexItems.isNotEmpty) {
      if (simpleItems.isNotEmpty) {
        widgets.add(const SizedBox(height: 8));
      }
      widgets.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.restaurant,
              color: Colors.orange.shade600,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'يحتاج تحضير: ${complexItems.join('، ')}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.orange.shade700,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return widgets;
  }

  bool _isSimpleItem(String item) {
    // Define simple items that don't need preparation
    final simpleKeywords = [
      'موز', 'تفاح', 'برتقال', 'عنب', 'توت',
      'حليب', 'زبادي', 'جبن', 'عسل',
      'خبز', 'بسكويت', 'مكسرات', 'لوز', 'جوز',
      'ماء', 'عصير', 'شاي', 'قهوة'
    ];

    return simpleKeywords.any((keyword) => item.contains(keyword));
  }

  int _getComplexItemsCount(GeneratedMeal meal) {
    return meal.ingredients.length;
  }

  bool _hasComplexPreparation(GeneratedMeal meal) {
    // Check if meal has items that need cooking/preparation
    final complexKeywords = [
      'شوفان', 'أرز', 'عدس', 'دجاج', 'سمك', 'لحم',
      'بيض', 'خضار', 'سلطة', 'شوربة', 'عجة'
    ];

    return meal.ingredients.any((ingredient) =>
        complexKeywords.any((keyword) => ingredient.contains(keyword))) ||
        meal.instructions.isNotEmpty;
  }

  String _getComplexItemName(GeneratedMeal meal) {
    // Extract the main complex item name from the meal
    if (meal.name.contains('شوفان')) return 'الشوفان';
    if (meal.name.contains('سلطة')) return 'السلطة';
    if (meal.name.contains('شوربة')) return 'الشوربة';
    if (meal.name.contains('عجة')) return 'العجة';
    if (meal.name.contains('سمك') || meal.name.contains('سلمون')) return 'السمك';
    if (meal.name.contains('دجاج')) return 'الدجاج';
    if (meal.name.contains('لحم')) return 'اللحم';
    if (meal.name.contains('أرز')) return 'الأرز';

    // Default to the first word of the meal name
    final words = meal.name.split(' ');
    return words.isNotEmpty ? words.first : 'الطبق الرئيسي';
  }

  List<String> _getComplexItemIngredients(GeneratedMeal meal) {
    // Filter ingredients that are needed for the complex preparation
    final complexKeywords = [
      'شوفان', 'أرز', 'عدس', 'دجاج', 'سمك', 'لحم',
      'بيض', 'زيت', 'ملح', 'فلفل', 'بصل', 'ثوم'
    ];

    return meal.ingredients.where((ingredient) =>
        complexKeywords.any((keyword) => ingredient.contains(keyword))).toList();
  }

  List<String> _getComplexItemInstructions(GeneratedMeal meal) {
    // Return the cooking instructions
    return meal.instructions;
  }

  // Helper methods for meal details dialog
  Widget _buildQuickInfo(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.white70,
          size: 16,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildNutritionDetail(String label, String value, String unit) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        Text(
          unit,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Gradient _getMealGradient(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return LinearGradient(
          colors: [Colors.orange.shade400, Colors.orange.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'lunch':
        return LinearGradient(
          colors: [Colors.blue.shade400, Colors.blue.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'dinner':
        return LinearGradient(
          colors: [Colors.indigo.shade400, Colors.indigo.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'snack':
        return LinearGradient(
          colors: [Colors.green.shade400, Colors.green.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppColors.primaryGradient;
    }
  }

  IconData _getMealIcon(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return Icons.wb_sunny;
      case 'lunch':
        return Icons.wb_sunny_outlined;
      case 'dinner':
        return Icons.nightlight;
      case 'snack':
        return Icons.local_cafe;
      default:
        return Icons.restaurant;
    }
  }

  String _getMealTypeDisplayName(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return 'الإفطار';
      case 'lunch':
        return 'الغداء';
      case 'dinner':
        return 'العشاء';
      case 'snack':
        return 'وجبة خفيفة';
      default:
        return mealType;
    }
  }
}
