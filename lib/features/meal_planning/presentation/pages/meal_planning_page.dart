import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/extensions.dart';
import '../../data/models/meal_plan_request.dart';
import '../providers/meal_plan_generation_provider.dart';

class MealPlanningPage extends ConsumerStatefulWidget {
  const MealPlanningPage({super.key});

  @override
  ConsumerState<MealPlanningPage> createState() => _MealPlanningPageState();
}

class _MealPlanningPageState extends ConsumerState<MealPlanningPage> {
  late PageController _pageController;
  int _currentPageIndex = 0;
  bool _isGeneratingExtraDays = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    // Load user preferences when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(mealPlanGenerationNotifierProvider.notifier).loadUserPreferences();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(mealPlanGenerationNotifierProvider);
    final notifier = ref.read(mealPlanGenerationNotifierProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: Text(state.isGenerating ? 'إنشاء خطة وجبات' : 'خطة الوجبات'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: state.isGenerating
        ? _buildGeneratingView()
        : _buildMealPlanningOverview(state, notifier),
    );
  }

  /// Start meal plan generation immediately using saved preferences
  void _startMealPlanGeneration(MealPlanGenerationNotifier notifier) {
    // Reset page controller and current page
    setState(() {
      _currentPageIndex = 0;
      _isGeneratingExtraDays = false;
    });

    // Load user preferences and start generation
    notifier.loadUserPreferences();
    notifier.generateMealPlan();
  }

  /// Generate extra 3 days for the meal plan
  Future<void> _generateExtraDays() async {
    setState(() {
      _isGeneratingExtraDays = true;
    });

    try {
      final currentPlan = ref.read(mealPlanGenerationNotifierProvider).generatedPlan;
      final currentDaysCount = currentPlan?.mealPlan.days.length ?? 0;

      final notifier = ref.read(mealPlanGenerationNotifierProvider.notifier);
      await notifier.generateExtraDays();

      // Navigate to the first new day after generation
      if (mounted) {
        await Future.delayed(const Duration(milliseconds: 500));
        _pageController.animateToPage(
          currentDaysCount, // Navigate to the first new day (index starts from 0)
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء أيام إضافية: $e'),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingExtraDays = false;
        });
      }
    }
  }

  Widget _buildGeneratingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // AI Logo/Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.auto_awesome,
              size: 60,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 32),

          // Loading animation
          SizedBox(
            width: 40,
            height: 40,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),
          const SizedBox(height: 24),

          // Loading text
          Text(
            'جاري إنشاء خطة وجبات مخصصة لك...',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            'يتم تحليل تفضيلاتك وإنشاء خطة مثالية باستخدام الذكاء الاصطناعي',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMealPlanningOverview(MealPlanGenerationState state, MealPlanGenerationNotifier notifier) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [


          // Error Display
          if (state.error != null) ...[
            _buildErrorCard(state.error!),
            const SizedBox(height: 24),
          ],

          // Generated Plan Display
          if (state.generatedPlan != null) ...[
            _buildGeneratedPlanCard(state.generatedPlan!),
            const SizedBox(height: 24),
          ],

          // My Meal Plans Section (only show if no generated plan)
          if (state.generatedPlan == null) ...[
            Text(
              'خططي السابقة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Empty state for previous plans
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(40),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.restaurant_menu_outlined,
                    size: 64,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد خطط وجبات حتى الآن',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ابدأ بإنشاء خطة وجبات جديدة',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorCard(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حدث خطأ',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  error,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneratedPlanCard(MealPlanResponse plan) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Success Header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green.shade600,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'تم إنشاء خطة الوجبات بنجاح!',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                plan.message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.green.shade600,
                ),
              ),
              const SizedBox(height: 16),

              // Save Plan Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Save plan
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم حفظ الخطة بنجاح'),
                        backgroundColor: Colors.green.shade600,
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: Icon(Icons.bookmark),
                  label: Text('حفظ الخطة'),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Daily Meal Plan PageView
        if (plan.mealPlan.days.isNotEmpty) ...[
          Text(
            'خطة الوجبات اليومية',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          _buildMealPlanPageView(plan),
        ],
      ],
    );
  }

  Widget _buildMealPlanPageView(MealPlanResponse plan) {
    final totalDays = plan.mealPlan.days.length;
    final hasMoreDays = totalDays > 3; // Check if we have more than initial 3 days

    return Column(
      children: [
        // Page indicator and navigation
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Navigation button (left)
            IconButton(
              onPressed: _currentPageIndex > 0 ? () {
                _pageController.previousPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              } : null,
              icon: Icon(
                Icons.arrow_back_ios,
                color: _currentPageIndex > 0 ? AppColors.primary : Colors.grey,
              ),
            ),

            // Page indicator
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (int i = 0; i < totalDays + 1; i++) // +1 for confirmation page
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: i == _currentPageIndex
                          ? AppColors.primary
                          : Colors.grey.shade300,
                      ),
                    ),
                  // Show loading indicator if generating extra days
                  if (_isGeneratingExtraDays)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    ),
                ],
              ),
            ),

            // Navigation button (right)
            IconButton(
              onPressed: _currentPageIndex < totalDays ? () {
                _pageController.nextPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              } : null,
              icon: Icon(
                Icons.arrow_forward_ios,
                color: _currentPageIndex < totalDays ? AppColors.primary : Colors.grey,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // PageView for meal plan days
        SizedBox(
          height: 600, // Fixed height for the PageView
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPageIndex = index;
              });

              // Check if we reached the last actual day and need to show confirmation page
              if (index == totalDays && !_isGeneratingExtraDays) {
                // This will be the confirmation page
              }
            },
            itemCount: totalDays + 1, // Add one for the confirmation page
            itemBuilder: (context, index) {
              if (index < totalDays) {
                // Regular day card
                final day = plan.mealPlan.days[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: _buildDayMealCard(day),
                );
              } else {
                // Confirmation page for generating extra days
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: _buildGenerateExtraDaysCard(),
                );
              }
            },
          ),
        ),

        const SizedBox(height: 16),

        // Day counter and info
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            _currentPageIndex < totalDays
              ? 'اليوم ${_currentPageIndex + 1} من ${totalDays}'
              : 'إضافة أيام جديدة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDayMealCard(DayMealPlan day) {
    final now = DateTime.now();
    final dayDate = now.add(Duration(days: day.day - 1));
    final arabicDateFormat = DateFormat('EEEE، d MMMM yyyy', 'ar');

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day Header (Fixed)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${day.day}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'اليوم ${day.day}',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            arabicDateFormat.format(dayDate),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Nutrition Summary
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildNutritionItem('السعرات', '${day.totalNutrition.calories}', 'سعرة'),
                      _buildNutritionItem('البروتين', '${day.totalNutrition.protein.toStringAsFixed(0)}', 'جم'),
                      _buildNutritionItem('الكربوهيدرات', '${day.totalNutrition.carbs.toStringAsFixed(0)}', 'جم'),
                      _buildNutritionItem('الدهون', '${day.totalNutrition.fat.toStringAsFixed(0)}', 'جم'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Scrollable Meals Summary
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الوجبات (${day.meals.length})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),

                  ...day.meals.map((meal) => _buildMealSummaryItem(meal)).toList(),

                  const SizedBox(height: 16),

                  // Check Details Button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => _showDayDetailsDialog(day, dayDate),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primary,
                        side: BorderSide(color: AppColors.primary),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      icon: Icon(Icons.visibility),
                      label: Text('عرض التفاصيل'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateExtraDaysCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.add_circle_outline,
              size: 40,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 24),

          // Title
          Text(
            'إضافة أيام جديدة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'هل تريد إضافة 3 أيام إضافية لخطة الوجبات الخاصة بك؟\nسيتم إنشاء وجبات جديدة ومتنوعة لك.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 32),

          // Generate Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isGeneratingExtraDays ? null : () => _generateExtraDays(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: _isGeneratingExtraDays
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(Icons.restaurant_menu),
                label: Text(
                  _isGeneratingExtraDays ? 'جاري الإنشاء...' : 'إنشاء 3 أيام إضافية',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Cancel/Back Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isGeneratingExtraDays ? null : () {
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.textSecondary,
                  side: BorderSide(color: AppColors.textSecondary),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: Icon(Icons.arrow_back),
                label: Text('العودة للخطة الحالية'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionItem(String label, String value, String unit) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          unit,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildMealSummaryItem(GeneratedMeal meal) {
    IconData mealIcon;
    Color mealColor;

    switch (meal.type.toLowerCase()) {
      case 'breakfast':
        mealIcon = Icons.wb_sunny;
        mealColor = Colors.orange;
        break;
      case 'lunch':
        mealIcon = Icons.wb_sunny_outlined;
        mealColor = Colors.blue;
        break;
      case 'dinner':
        mealIcon = Icons.nightlight;
        mealColor = Colors.indigo;
        break;
      case 'snack':
        mealIcon = Icons.local_cafe;
        mealColor = Colors.green;
        break;
      default:
        mealIcon = Icons.restaurant;
        mealColor = Colors.grey;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: mealColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: mealColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: mealColor.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              mealIcon,
              color: mealColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  meal.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${meal.nutrition.calories} سعرة • ${meal.preparationTime} دقيقة',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDayDetailsDialog(DayMealPlan day, DateTime dayDate) {
    final arabicDateFormat = DateFormat('EEEE، d MMMM yyyy', 'ar');

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'اليوم ${day.day}',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                arabicDateFormat.format(dayDate),
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Nutrition Summary
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildNutritionItem('السعرات', '${day.totalNutrition.calories}', 'سعرة'),
                          _buildNutritionItem('البروتين', '${day.totalNutrition.protein.toStringAsFixed(0)}', 'جم'),
                          _buildNutritionItem('الكربوهيدرات', '${day.totalNutrition.carbs.toStringAsFixed(0)}', 'جم'),
                          _buildNutritionItem('الدهون', '${day.totalNutrition.fat.toStringAsFixed(0)}', 'جم'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Meals Details
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(20),
                  itemCount: day.meals.length,
                  itemBuilder: (context, index) {
                    final meal = day.meals[index];
                    return _buildDetailedMealCard(meal);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailedMealCard(GeneratedMeal meal) {
    IconData mealIcon;
    Color mealColor;
    String mealTypeArabic;

    switch (meal.type.toLowerCase()) {
      case 'breakfast':
        mealIcon = Icons.wb_sunny;
        mealColor = Colors.orange;
        mealTypeArabic = 'إفطار';
        break;
      case 'lunch':
        mealIcon = Icons.wb_sunny_outlined;
        mealColor = Colors.blue;
        mealTypeArabic = 'غداء';
        break;
      case 'dinner':
        mealIcon = Icons.nightlight;
        mealColor = Colors.indigo;
        mealTypeArabic = 'عشاء';
        break;
      case 'snack':
        mealIcon = Icons.local_cafe;
        mealColor = Colors.green;
        mealTypeArabic = 'وجبة خفيفة';
        break;
      default:
        mealIcon = Icons.restaurant;
        mealColor = Colors.grey;
        mealTypeArabic = 'وجبة';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: mealColor.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Meal Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: mealColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: mealColor.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    mealIcon,
                    color: mealColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        mealTypeArabic,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: mealColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        meal.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (meal.description != null)
                        Text(
                          meal.description!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Meal Details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Nutrition Info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildNutritionDetail('السعرات', '${meal.nutrition.calories}', 'سعرة', Colors.red),
                      _buildNutritionDetail('البروتين', '${meal.nutrition.protein.toStringAsFixed(0)}', 'جم', Colors.blue),
                      _buildNutritionDetail('الكربوهيدرات', '${meal.nutrition.carbs.toStringAsFixed(0)}', 'جم', Colors.orange),
                      _buildNutritionDetail('الدهون', '${meal.nutrition.fat.toStringAsFixed(0)}', 'جم', Colors.green),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Preparation Info
                Row(
                  children: [
                    Icon(Icons.timer, size: 16, color: AppColors.textSecondary),
                    const SizedBox(width: 4),
                    Text(
                      '${meal.preparationTime} دقيقة',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.bar_chart, size: 16, color: AppColors.textSecondary),
                    const SizedBox(width: 4),
                    Text(
                      meal.difficulty == 'easy' ? 'سهل' : meal.difficulty == 'medium' ? 'متوسط' : 'صعب',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Ingredients
                Text(
                  'المكونات:',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ...meal.ingredients.map((ingredient) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('• ', style: TextStyle(color: mealColor, fontWeight: FontWeight.bold)),
                      Expanded(
                        child: Text(
                          ingredient,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                )).toList(),

                const SizedBox(height: 16),

                // Instructions
                Text(
                  'طريقة التحضير:',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ...meal.instructions.asMap().entries.map((entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: mealColor,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '${entry.key + 1}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          entry.value,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                )).toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionDetail(String label, String value, String unit, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          unit,
          style: TextStyle(
            color: color.withOpacity(0.7),
            fontSize: 10,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 10,
          ),
        ),
      ],
    );
  }
}